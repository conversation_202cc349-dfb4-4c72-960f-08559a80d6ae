{"version": 3, "sources": [], "sections": [{"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/app/about/page.tsx"], "sourcesContent": ["export default function About() {\n  return (\n    <div className=\"min-h-screen bg-zinc-50 dark:bg-zinc-900\">\n      {/* Navigation */}\n      <nav className=\"border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center gap-2\">\n              <a href=\"/\" className=\"flex items-center gap-2\">\n                <div className=\"w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center\">\n                  <span className=\"text-white dark:text-black font-bold text-lg\">S</span>\n                </div>\n                <span className=\"text-xl font-bold text-black dark:text-white\">Stacks</span>\n              </a>\n            </div>\n            <div className=\"flex items-center gap-6\">\n              <a href=\"/#features\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Features\n              </a>\n              <a href=\"/#how-it-works\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                How It Works\n              </a>\n              <a href=\"/pricing\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Pricing\n              </a>\n              <a href=\"/about\" className=\"text-black dark:text-white font-medium transition-colors\">\n                About\n              </a>\n              <a href=\"/login\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Log In\n              </a>\n              <a href=\"/signup\" className=\"px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-medium\">\n                Sign Up\n              </a>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12\">\n        <div className=\"text-center\">\n          <h1 className=\"text-5xl sm:text-6xl font-bold text-black dark:text-white mb-6\">\n            Breaking Down Barriers,\n            <br />\n            Building Connections\n          </h1>\n          <p className=\"text-xl text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto\">\n            We believe that language should never be a barrier to great collaboration. \n            Stacks was built to empower teams around the world to work together seamlessly.\n          </p>\n        </div>\n      </section>\n\n      {/* Mission Section */}\n      <section className=\"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-12\">\n          <h2 className=\"text-3xl font-bold text-black dark:text-white mb-6 text-center\">\n            Our Mission\n          </h2>\n          <p className=\"text-lg text-zinc-700 dark:text-zinc-300 leading-relaxed text-center max-w-3xl mx-auto\">\n            To make project management truly global by eliminating language barriers through intelligent, \n            context-aware AI translation. We're building a world where every team member can contribute \n            in their native language while staying perfectly in sync with their colleagues across the globe.\n          </p>\n        </div>\n      </section>\n\n      {/* Values Section */}\n      <section className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <h2 className=\"text-4xl font-bold text-black dark:text-white mb-12 text-center\">\n          Our Values\n        </h2>\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8\">\n            <div className=\"text-4xl mb-4\">🌍</div>\n            <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n              Global First\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              We design for diversity from day one. Every feature is built with multilingual, \n              multicultural teams in mind.\n            </p>\n          </div>\n\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8\">\n            <div className=\"text-4xl mb-4\">🎯</div>\n            <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n              Accuracy Matters\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              We're obsessed with translation quality. Our AI understands context, tone, \n              and technical terminology to deliver precise translations.\n            </p>\n          </div>\n\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8\">\n            <div className=\"text-4xl mb-4\">⚡</div>\n            <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n              Speed & Simplicity\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Translation should be invisible. We make it instant and automatic so teams \n              can focus on what matters: getting work done.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Story Section */}\n      <section className=\"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-12\">\n          <h2 className=\"text-3xl font-bold text-black dark:text-white mb-6\">\n            Our Story\n          </h2>\n          <div className=\"space-y-4 text-zinc-700 dark:text-zinc-300 leading-relaxed\">\n            <p>\n              Stacks was born from a simple frustration: watching talented teams struggle to collaborate \n              because they spoke different languages. We saw engineers in Tokyo waiting hours for translations \n              from designers in Berlin, and product managers in São Paulo missing crucial context from \n              developers in San Francisco.\n            </p>\n            <p>\n              We knew there had to be a better way. Traditional translation tools were too slow and lacked \n              the context needed for technical project management. Generic project management tools ignored \n              the language problem entirely.\n            </p>\n            <p>\n              So we built Stacks: a project management platform that treats multilingual collaboration as \n              a first-class feature, not an afterthought. Using advanced AI that understands project context, \n              technical terminology, and team dynamics, we make it possible for anyone to work in their \n              preferred language while staying perfectly synchronized with their global team.\n            </p>\n            <p>\n              Today, teams from over 40 countries use Stacks to collaborate without language barriers. \n              We're just getting started.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"bg-black dark:bg-white rounded-lg p-12 text-center border-2 border-black dark:border-white\">\n          <h2 className=\"text-4xl font-bold text-white dark:text-black mb-4\">\n            Join Us in Building the Future\n          </h2>\n          <p className=\"text-xl text-zinc-300 dark:text-zinc-700 mb-8 max-w-2xl mx-auto\">\n            Be part of a global movement to make collaboration truly borderless\n          </p>\n          <button className=\"px-8 py-4 bg-white dark:bg-black text-black dark:text-white rounded hover:bg-zinc-100 dark:hover:bg-zinc-900 transition-all font-semibold text-lg\">\n            Start Your Free Trial\n          </button>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"border-t border-zinc-200 dark:border-zinc-800 py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center text-zinc-600 dark:text-zinc-400\">\n            <p>&copy; 2025 Stacks. Built for multilingual teams everywhere.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,MAAK;oCAAI,WAAU;;sDACpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+C;;;;;;;;;;;sDAEjE,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAa,WAAU;kDAA4F;;;;;;kDAG3H,8OAAC;wCAAE,MAAK;wCAAiB,WAAU;kDAA4F;;;;;;kDAG/H,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA4F;;;;;;kDAGzH,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA2D;;;;;;kDAGtF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA4F;;;;;;kDAGvH,8OAAC;wCAAE,MAAK;wCAAU,WAAU;kDAA0I;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9K,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAiE;8CAE7E,8OAAC;;;;;gCAAK;;;;;;;sCAGR,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;;;;;;0BAQ9E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,8OAAC;4BAAE,WAAU;sCAAyF;;;;;;;;;;;;;;;;;0BAS1G,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAkE;;;;;;kCAGhF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAMlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAMlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;0BAStD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CAMH,8OAAC;8CAAE;;;;;;8CAKH,8OAAC;8CAAE;;;;;;8CAMH,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAE,WAAU;sCAAkE;;;;;;sCAG/E,8OAAC;4BAAO,WAAU;sCAAoJ;;;;;;;;;;;;;;;;;0BAO1K,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf"}}]}