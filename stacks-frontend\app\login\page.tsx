"use client";

import { useState } from "react";

export default function Login() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle login logic here
    console.log("Login data:", formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.type === "checkbox" ? e.target.checked : e.target.value;
    setFormData({
      ...formData,
      [e.target.name]: value,
    });
  };

  return (
    <div className="min-h-screen bg-zinc-50 dark:bg-zinc-900 flex flex-col">
      {/* Navigation */}
      <nav className="border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-2">
              <a href="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center">
                  <span className="text-white dark:text-black font-bold text-lg">S</span>
                </div>
                <span className="text-xl font-bold text-black dark:text-white">Stacks</span>
              </a>
            </div>
            <div className="flex items-center gap-6">
              <a href="/pricing" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Pricing
              </a>
              <a href="/about" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                About
              </a>
              <a href="/signup" className="px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-medium">
                Sign Up
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Login Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-black dark:text-white mb-2">
              Welcome Back
            </h1>
            <p className="text-zinc-600 dark:text-zinc-400">
              Log in to your Stacks account
            </p>
          </div>

          <div className="bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-black dark:text-white mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-black dark:text-white mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors"
                  placeholder="••••••••"
                />
              </div>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="rememberMe"
                    name="rememberMe"
                    checked={formData.rememberMe}
                    onChange={handleChange}
                    className="w-4 h-4 rounded border-zinc-300 dark:border-zinc-600 text-black focus:ring-black dark:focus:ring-white"
                  />
                  <label htmlFor="rememberMe" className="ml-2 text-sm text-zinc-700 dark:text-zinc-300">
                    Remember me
                  </label>
                </div>
                <a href="#" className="text-sm text-black dark:text-white hover:underline">
                  Forgot password?
                </a>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                className="w-full px-6 py-3 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-semibold"
              >
                Log In
              </button>
            </form>

            {/* Divider */}
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-zinc-300 dark:border-zinc-600"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-zinc-800 text-zinc-600 dark:text-zinc-400">
                  Or continue with
                </span>
              </div>
            </div>

            {/* Social Login Buttons */}
            <div className="space-y-3">
              <button className="w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-zinc-300 dark:border-zinc-600 rounded hover:border-black dark:hover:border-white transition-all font-medium flex items-center justify-center gap-2">
                <span>🔍</span>
                Continue with Google
              </button>
              <button className="w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-zinc-300 dark:border-zinc-600 rounded hover:border-black dark:hover:border-white transition-all font-medium flex items-center justify-center gap-2">
                <span>💼</span>
                Continue with Microsoft
              </button>
            </div>

            {/* Signup Link */}
            <p className="text-center text-zinc-600 dark:text-zinc-400 mt-6">
              Don't have an account?{" "}
              <a href="/signup" className="text-black dark:text-white font-semibold hover:underline">
                Sign up
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

