   0.616888100s  INFO prepare_target{force=false package_id=backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend) target="backend"}: cargo::core::compiler::fingerprint: fingerprint error for backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend)/Check { test: false }/TargetInner { name: "backend", doc: true, ..: with_path("C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\src\\main.rs", Edition2024) }
   0.617179900s  INFO prepare_target{force=false package_id=backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend) target="backend"}: cargo::core::compiler::fingerprint:     err: failed to read `C:\Users\<USER>\Projects\projects\stacks\backend\target\debug\.fingerprint\backend-ecaa9b012f610415\bin-backend`

Caused by:
    The system cannot find the file specified. (os error 2)

Stack backtrace:
   0: git_midx_writer_dump
   1: git_midx_writer_dump
   2: git_midx_writer_dump
   3: git_midx_writer_dump
   4: git_filter_source_repo
   5: git_filter_source_path
   6: git_filter_source_path
   7: git_filter_source_path
   8: git_filter_source_path
   9: git_filter_source_repo
  10: git_filter_source_repo
  11: git_filter_source_repo
  12: git_filter_source_repo
  13: git_filter_source_repo
  14: <unknown>
  15: <unknown>
  16: git_midx_writer_dump
  17: git_filter_source_repo
  18: git_midx_writer_dump
  19: BaseThreadInitThunk
  20: RtlUserThreadStart
   0.634078400s  INFO prepare_target{force=false package_id=backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend) target="build-script-build"}: cargo::core::compiler::fingerprint: fingerprint error for backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend)/RunCustomBuild/TargetInner { ..: custom_build_target("build-script-build", "C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\build.rs", Edition2024) }
   0.634114100s  INFO prepare_target{force=false package_id=backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend) target="build-script-build"}: cargo::core::compiler::fingerprint:     err: failed to read `C:\Users\<USER>\Projects\projects\stacks\backend\target\debug\.fingerprint\backend-6295f66394c9585e\run-build-script-build-script-build`

Caused by:
    The system cannot find the file specified. (os error 2)

Stack backtrace:
   0: git_midx_writer_dump
   1: git_midx_writer_dump
   2: git_midx_writer_dump
   3: git_midx_writer_dump
   4: git_filter_source_repo
   5: git_filter_source_path
   6: git_filter_source_repo
   7: git_filter_source_path
   8: git_filter_source_path
   9: git_filter_source_path
  10: git_filter_source_path
  11: git_filter_source_repo
  12: git_filter_source_repo
  13: git_filter_source_repo
  14: git_filter_source_repo
  15: git_filter_source_repo
  16: <unknown>
  17: <unknown>
  18: git_midx_writer_dump
  19: git_filter_source_repo
  20: git_midx_writer_dump
  21: BaseThreadInitThunk
  22: RtlUserThreadStart
   1.602303900s  INFO prepare_target{force=false package_id=backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend) target="backend"}: cargo::core::compiler::fingerprint: fingerprint error for backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend)/Check { test: true }/TargetInner { name: "backend", doc: true, ..: with_path("C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\src\\main.rs", Edition2024) }
   1.602332600s  INFO prepare_target{force=false package_id=backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend) target="backend"}: cargo::core::compiler::fingerprint:     err: failed to read `C:\Users\<USER>\Projects\projects\stacks\backend\target\debug\.fingerprint\backend-efd5d3ec57913f14\test-bin-backend`

Caused by:
    The system cannot find the file specified. (os error 2)

Stack backtrace:
   0: git_midx_writer_dump
   1: git_midx_writer_dump
   2: git_midx_writer_dump
   3: git_midx_writer_dump
   4: git_filter_source_repo
   5: git_filter_source_path
   6: git_filter_source_path
   7: git_filter_source_path
   8: git_filter_source_path
   9: git_filter_source_repo
  10: git_filter_source_repo
  11: git_filter_source_repo
  12: git_filter_source_repo
  13: git_filter_source_repo
  14: <unknown>
  15: <unknown>
  16: git_midx_writer_dump
  17: git_filter_source_repo
  18: git_midx_writer_dump
  19: BaseThreadInitThunk
  20: RtlUserThreadStart
   Compiling backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend)
error: failed to run custom build command for `backend v0.1.0 (C:\Users\<USER>\Projects\projects\stacks\backend)`
note: To improve backtraces for build dependencies, set the CARGO_PROFILE_DEV_BUILD_OVERRIDE_DEBUG=true environment variable to enable debug information generation.

Caused by:
  process didn't exit successfully: `C:\Users\<USER>\Projects\projects\stacks\backend\target\debug\build\backend-8c21dfa211ef7785\build-script-build` (exit code: 101)
  --- stderr

  thread 'main' panicked at build.rs:16:10:
  Failed to compile protos: Custom { kind: NotFound, error: "Could not find `protoc`. If `protoc` is installed, try setting the `PROTOC` environment variable to the path of the `protoc` binary. Try installing `protobuf-compiler` or `protobuf` using your package manager. It is also available at https://github.com/protocolbuffers/protobuf/releases  For more information: https://docs.rs/prost-build/#sourcing-protoc" }
  stack backtrace:
     0: std::panicking::begin_panic_handler
               at /rustc/1159e78c4747b02ef996e55082b704c09b970588/library\std\src\panicking.rs:697
     1: core::panicking::panic_fmt
               at /rustc/1159e78c4747b02ef996e55082b704c09b970588/library\core\src\panicking.rs:75
     2: core::result::unwrap_failed
               at /rustc/1159e78c4747b02ef996e55082b704c09b970588/library\core\src\result.rs:1765
     3: core::result::Result<T,E>::expect
     4: build_script_build::main
     5: core::ops::function::FnOnce::call_once
  note: Some details are omitted, run with `RUST_BACKTRACE=full` for a verbose backtrace.
