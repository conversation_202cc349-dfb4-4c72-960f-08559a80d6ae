export default function About() {
  return (
    <div className="min-h-screen bg-zinc-50 dark:bg-zinc-900">
      {/* Navigation */}
      <nav className="border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-2">
              <a href="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center">
                  <span className="text-white dark:text-black font-bold text-lg">S</span>
                </div>
                <span className="text-xl font-bold text-black dark:text-white">Stacks</span>
              </a>
            </div>
            <div className="flex items-center gap-6">
              <a href="/#features" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Features
              </a>
              <a href="/#how-it-works" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                How It Works
              </a>
              <a href="/pricing" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Pricing
              </a>
              <a href="/about" className="text-black dark:text-white font-medium transition-colors">
                About
              </a>
              <a href="/login" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Log In
              </a>
              <a href="/signup" className="px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-medium">
                Sign Up
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        <div className="text-center">
          <h1 className="text-5xl sm:text-6xl font-bold text-black dark:text-white mb-6">
            Breaking Down Barriers,
            <br />
            Building Connections
          </h1>
          <p className="text-xl text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto">
            We believe that language should never be a barrier to great collaboration. 
            Stacks was built to empower teams around the world to work together seamlessly.
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-12">
          <h2 className="text-3xl font-bold text-black dark:text-white mb-6 text-center">
            Our Mission
          </h2>
          <p className="text-lg text-zinc-700 dark:text-zinc-300 leading-relaxed text-center max-w-3xl mx-auto">
            To make project management truly global by eliminating language barriers through intelligent, 
            context-aware AI translation. We're building a world where every team member can contribute 
            in their native language while staying perfectly in sync with their colleagues across the globe.
          </p>
        </div>
      </section>

      {/* Values Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 className="text-4xl font-bold text-black dark:text-white mb-12 text-center">
          Our Values
        </h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8">
            <div className="text-4xl mb-4">🌍</div>
            <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
              Global First
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              We design for diversity from day one. Every feature is built with multilingual, 
              multicultural teams in mind.
            </p>
          </div>

          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8">
            <div className="text-4xl mb-4">🎯</div>
            <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
              Accuracy Matters
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              We're obsessed with translation quality. Our AI understands context, tone, 
              and technical terminology to deliver precise translations.
            </p>
          </div>

          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8">
            <div className="text-4xl mb-4">⚡</div>
            <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
              Speed & Simplicity
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              Translation should be invisible. We make it instant and automatic so teams 
              can focus on what matters: getting work done.
            </p>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-12">
          <h2 className="text-3xl font-bold text-black dark:text-white mb-6">
            Our Story
          </h2>
          <div className="space-y-4 text-zinc-700 dark:text-zinc-300 leading-relaxed">
            <p>
              Stacks was born from a simple frustration: watching talented teams struggle to collaborate 
              because they spoke different languages. We saw engineers in Tokyo waiting hours for translations 
              from designers in Berlin, and product managers in São Paulo missing crucial context from 
              developers in San Francisco.
            </p>
            <p>
              We knew there had to be a better way. Traditional translation tools were too slow and lacked 
              the context needed for technical project management. Generic project management tools ignored 
              the language problem entirely.
            </p>
            <p>
              So we built Stacks: a project management platform that treats multilingual collaboration as 
              a first-class feature, not an afterthought. Using advanced AI that understands project context, 
              technical terminology, and team dynamics, we make it possible for anyone to work in their 
              preferred language while staying perfectly synchronized with their global team.
            </p>
            <p>
              Today, teams from over 40 countries use Stacks to collaborate without language barriers. 
              We're just getting started.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="bg-black dark:bg-white rounded-lg p-12 text-center border-2 border-black dark:border-white">
          <h2 className="text-4xl font-bold text-white dark:text-black mb-4">
            Join Us in Building the Future
          </h2>
          <p className="text-xl text-zinc-300 dark:text-zinc-700 mb-8 max-w-2xl mx-auto">
            Be part of a global movement to make collaboration truly borderless
          </p>
          <button className="px-8 py-4 bg-white dark:bg-black text-black dark:text-white rounded hover:bg-zinc-100 dark:hover:bg-zinc-900 transition-all font-semibold text-lg">
            Start Your Free Trial
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-zinc-200 dark:border-zinc-800 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-zinc-600 dark:text-zinc-400">
            <p>&copy; 2025 Stacks. Built for multilingual teams everywhere.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

