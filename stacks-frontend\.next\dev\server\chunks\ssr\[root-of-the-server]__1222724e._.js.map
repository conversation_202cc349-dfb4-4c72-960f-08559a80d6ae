{"version": 3, "sources": [], "sections": [{"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-zinc-50 dark:bg-zinc-900\">\n      {/* Navigation */}\n      <nav className=\"border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center\">\n                <span className=\"text-white dark:text-black font-bold text-lg\">S</span>\n              </div>\n              <span className=\"text-xl font-bold text-black dark:text-white\">Stacks</span>\n            </div>\n            <div className=\"flex items-center gap-6\">\n              <a href=\"/pricing\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Pricing\n              </a>\n              <a href=\"/about\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                About\n              </a>\n              <a href=\"/login\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Log In\n              </a>\n              <a href=\"/signup\" className=\"px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-medium\">\n                Sign Up\n              </a>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16\">\n        <div className=\"text-center\">\n          <h1 className=\"text-5xl sm:text-6xl lg:text-7xl font-bold text-black dark:text-white mb-6 leading-tight\">\n            Project Management for\n            <br />\n            <span className=\"text-black dark:text-white\">Multilingual Teams</span>\n          </h1>\n          <p className=\"text-xl text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto mb-10 leading-relaxed\">\n            Break down language barriers with AI-powered translations. Collaborate seamlessly across languages and keep your global team in perfect sync.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <button className=\"px-8 py-4 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-semibold text-lg\">\n              Start Free Trial\n            </button>\n            <button className=\"px-8 py-4 bg-white dark:bg-zinc-800 text-black dark:text-white rounded border-2 border-zinc-300 dark:border-zinc-700 hover:border-black dark:hover:border-white transition-all font-semibold text-lg\">\n              Watch Demo\n            </button>\n          </div>\n        </div>\n\n        {/* Hero Image/Illustration Placeholder */}\n        <div className=\"mt-16 relative\">\n          <div className=\"bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8 min-h-[400px] flex items-center justify-center\">\n            <div className=\"text-center\">\n              <div className=\"text-6xl mb-4\">🌍 💬 🤝</div>\n              <p className=\"text-zinc-600 dark:text-zinc-400 text-lg\">Your multilingual project dashboard preview</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-bold text-black dark:text-white mb-4\">\n            Built for Global Collaboration\n          </h2>\n          <p className=\"text-xl text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto\">\n            Everything you need to manage projects across languages and cultures\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {/* Feature 1 */}\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8 hover:border-black dark:hover:border-white transition-all\">\n            <div className=\"w-12 h-12 bg-zinc-100 dark:bg-zinc-700 rounded flex items-center justify-center mb-4\">\n              <span className=\"text-2xl\">🤖</span>\n            </div>\n            <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n              AI-Powered Translation\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Automatic real-time translation of tasks, comments, and documents. Work in your language, collaborate with everyone.\n            </p>\n          </div>\n\n          {/* Feature 2 */}\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8 hover:border-black dark:hover:border-white transition-all\">\n            <div className=\"w-12 h-12 bg-zinc-100 dark:bg-zinc-700 rounded flex items-center justify-center mb-4\">\n              <span className=\"text-2xl\">⚡</span>\n            </div>\n            <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n              Real-Time Sync\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Updates appear instantly in every team member's preferred language. No delays, no confusion.\n            </p>\n          </div>\n\n          {/* Feature 3 */}\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8 hover:border-black dark:hover:border-white transition-all\">\n            <div className=\"w-12 h-12 bg-zinc-100 dark:bg-zinc-700 rounded flex items-center justify-center mb-4\">\n              <span className=\"text-2xl\">🎯</span>\n            </div>\n            <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n              Context-Aware\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Our AI understands project context and technical terms, ensuring accurate translations every time.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* How It Works Section */}\n      <section id=\"how-it-works\" className=\"bg-white dark:bg-zinc-800 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-black dark:text-white mb-4\">\n              How It Works\n            </h2>\n            <p className=\"text-xl text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto\">\n              Get started in minutes, collaborate across languages instantly\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-12\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-black dark:bg-white rounded-full flex items-center justify-center mx-auto mb-4 text-white dark:text-black text-2xl font-bold\">\n                1\n              </div>\n              <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n                Set Your Language\n              </h3>\n              <p className=\"text-zinc-600 dark:text-zinc-400\">\n                Choose your preferred language from 50+ supported languages\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-black dark:bg-white rounded-full flex items-center justify-center mx-auto mb-4 text-white dark:text-black text-2xl font-bold\">\n                2\n              </div>\n              <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n                Invite Your Team\n              </h3>\n              <p className=\"text-zinc-600 dark:text-zinc-400\">\n                Team members can work in their own language preferences\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-black dark:bg-white rounded-full flex items-center justify-center mx-auto mb-4 text-white dark:text-black text-2xl font-bold\">\n                3\n              </div>\n              <h3 className=\"text-xl font-semibold text-black dark:text-white mb-3\">\n                Collaborate Seamlessly\n              </h3>\n              <p className=\"text-zinc-600 dark:text-zinc-400\">\n                AI handles all translations automatically in the background\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"bg-black dark:bg-white rounded-lg p-12 text-center border-2 border-black dark:border-white\">\n          <h2 className=\"text-4xl font-bold text-white dark:text-black mb-4\">\n            Ready to Break Down Language Barriers?\n          </h2>\n          <p className=\"text-xl text-zinc-300 dark:text-zinc-700 mb-8 max-w-2xl mx-auto\">\n            Join teams from around the world who are collaborating without limits\n          </p>\n          <button className=\"px-8 py-4 bg-white dark:bg-black text-black dark:text-white rounded hover:bg-zinc-100 dark:hover:bg-zinc-900 transition-all font-semibold text-lg\">\n            Start Your Free Trial\n          </button>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"border-t border-zinc-200 dark:border-zinc-800 py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center text-zinc-600 dark:text-zinc-400\">\n            <p>&copy; 2025 Stacks. Built for multilingual teams everywhere.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;kDAEjE,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAEjE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA4F;;;;;;kDAGzH,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA4F;;;;;;kDAGvH,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA4F;;;;;;kDAGvH,8OAAC;wCAAE,MAAK;wCAAU,WAAU;kDAA0I;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9K,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA2F;kDAEvG,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;0CAE/C,8OAAC;gCAAE,WAAU;0CAAmF;;;;;;0CAGhG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAoJ;;;;;;kDAGtK,8OAAC;wCAAO,WAAU;kDAAuM;;;;;;;;;;;;;;;;;;kCAO7N,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;;kCAC/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;kDAE7B,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAMlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;kDAE7B,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAMlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;kDAE7B,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;0BAQtD,8OAAC;gBAAQ,IAAG;gBAAe,WAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CAA6D;;;;;;;;;;;;sCAK5E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA4I;;;;;;sDAG3J,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAGtE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAKlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA4I;;;;;;sDAG3J,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAGtE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAKlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA4I;;;;;;sDAG3J,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAGtE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAE,WAAU;sCAAkE;;;;;;sCAG/E,8OAAC;4BAAO,WAAU;sCAAoJ;;;;;;;;;;;;;;;;;0BAO1K,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf"}}]}