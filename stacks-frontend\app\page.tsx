export default function Home() {
  return (
    <div className="min-h-screen bg-zinc-50 dark:bg-zinc-900">
      {/* Navigation */}
      <nav className="border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center">
                <span className="text-white dark:text-black font-bold text-lg">S</span>
              </div>
              <span className="text-xl font-bold text-black dark:text-white">Stacks</span>
            </div>
            <div className="flex items-center gap-6">
              <a href="/pricing" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Pricing
              </a>
              <a href="/about" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                About
              </a>
              <a href="/login" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Log In
              </a>
              <a href="/signup" className="px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-medium">
                Sign Up
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="text-center">
          <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-black dark:text-white mb-6 leading-tight">
            Project Management for
            <br />
            <span className="text-black dark:text-white">Multilingual Teams</span>
          </h1>
          <p className="text-xl text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto mb-10 leading-relaxed">
            Break down language barriers with AI-powered translations. Collaborate seamlessly across languages and keep your global team in perfect sync.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="px-8 py-4 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-semibold text-lg">
              Start Free Trial
            </button>
            <button className="px-8 py-4 bg-white dark:bg-zinc-800 text-black dark:text-white rounded border-2 border-zinc-300 dark:border-zinc-700 hover:border-black dark:hover:border-white transition-all font-semibold text-lg">
              Watch Demo
            </button>
          </div>
        </div>

        {/* Hero Image/Illustration Placeholder */}
        <div className="mt-16 relative">
          <div className="bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8 min-h-[400px] flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">🌍 💬 🤝</div>
              <p className="text-zinc-600 dark:text-zinc-400 text-lg">Your multilingual project dashboard preview</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-black dark:text-white mb-4">
            Built for Global Collaboration
          </h2>
          <p className="text-xl text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
            Everything you need to manage projects across languages and cultures
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {/* Feature 1 */}
          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8 hover:border-black dark:hover:border-white transition-all">
            <div className="w-12 h-12 bg-zinc-100 dark:bg-zinc-700 rounded flex items-center justify-center mb-4">
              <span className="text-2xl">🤖</span>
            </div>
            <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
              AI-Powered Translation
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              Automatic real-time translation of tasks, comments, and documents. Work in your language, collaborate with everyone.
            </p>
          </div>

          {/* Feature 2 */}
          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8 hover:border-black dark:hover:border-white transition-all">
            <div className="w-12 h-12 bg-zinc-100 dark:bg-zinc-700 rounded flex items-center justify-center mb-4">
              <span className="text-2xl">⚡</span>
            </div>
            <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
              Real-Time Sync
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              Updates appear instantly in every team member's preferred language. No delays, no confusion.
            </p>
          </div>

          {/* Feature 3 */}
          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-8 hover:border-black dark:hover:border-white transition-all">
            <div className="w-12 h-12 bg-zinc-100 dark:bg-zinc-700 rounded flex items-center justify-center mb-4">
              <span className="text-2xl">🎯</span>
            </div>
            <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
              Context-Aware
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              Our AI understands project context and technical terms, ensuring accurate translations every time.
            </p>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="bg-white dark:bg-zinc-800 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-black dark:text-white mb-4">
              How It Works
            </h2>
            <p className="text-xl text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
              Get started in minutes, collaborate across languages instantly
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-black dark:bg-white rounded-full flex items-center justify-center mx-auto mb-4 text-white dark:text-black text-2xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
                Set Your Language
              </h3>
              <p className="text-zinc-600 dark:text-zinc-400">
                Choose your preferred language from 50+ supported languages
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-black dark:bg-white rounded-full flex items-center justify-center mx-auto mb-4 text-white dark:text-black text-2xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
                Invite Your Team
              </h3>
              <p className="text-zinc-600 dark:text-zinc-400">
                Team members can work in their own language preferences
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-black dark:bg-white rounded-full flex items-center justify-center mx-auto mb-4 text-white dark:text-black text-2xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-black dark:text-white mb-3">
                Collaborate Seamlessly
              </h3>
              <p className="text-zinc-600 dark:text-zinc-400">
                AI handles all translations automatically in the background
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="bg-black dark:bg-white rounded-lg p-12 text-center border-2 border-black dark:border-white">
          <h2 className="text-4xl font-bold text-white dark:text-black mb-4">
            Ready to Break Down Language Barriers?
          </h2>
          <p className="text-xl text-zinc-300 dark:text-zinc-700 mb-8 max-w-2xl mx-auto">
            Join teams from around the world who are collaborating without limits
          </p>
          <button className="px-8 py-4 bg-white dark:bg-black text-black dark:text-white rounded hover:bg-zinc-100 dark:hover:bg-zinc-900 transition-all font-semibold text-lg">
            Start Your Free Trial
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-zinc-200 dark:border-zinc-800 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-zinc-600 dark:text-zinc-400">
            <p>&copy; 2025 Stacks. Built for multilingual teams everywhere.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
