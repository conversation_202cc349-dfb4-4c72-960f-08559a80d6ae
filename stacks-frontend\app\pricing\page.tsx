export default function Pricing() {
  return (
    <div className="min-h-screen bg-zinc-50 dark:bg-zinc-900">
      {/* Navigation */}
      <nav className="border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-2">
              <a href="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center">
                  <span className="text-white dark:text-black font-bold text-lg">S</span>
                </div>
                <span className="text-xl font-bold text-black dark:text-white">Stacks</span>
              </a>
            </div>
            <div className="flex items-center gap-6">
              <a href="/#features" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Features
              </a>
              <a href="/#how-it-works" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                How It Works
              </a>
              <a href="/pricing" className="text-black dark:text-white font-medium transition-colors">
                Pricing
              </a>
              <a href="/about" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                About
              </a>
              <a href="/login" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Log In
              </a>
              <a href="/signup" className="px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-medium">
                Sign Up
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        <div className="text-center">
          <h1 className="text-5xl sm:text-6xl font-bold text-black dark:text-white mb-6">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
            Choose the plan that fits your team. All plans include AI-powered translations.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        <div className="grid md:grid-cols-3 gap-8">
          {/* Starter Plan */}
          <div className="bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8">
            <h3 className="text-2xl font-bold text-black dark:text-white mb-2">Starter</h3>
            <p className="text-zinc-600 dark:text-zinc-400 mb-6">Perfect for small teams</p>
            <div className="mb-6">
              <span className="text-5xl font-bold text-black dark:text-white">$29</span>
              <span className="text-zinc-600 dark:text-zinc-400">/month</span>
            </div>
            <ul className="space-y-4 mb-8">
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">Up to 5 team members</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">2 languages supported</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">Basic project management</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">AI translations (1,000/month)</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">Email support</span>
              </li>
            </ul>
            <button className="w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-black dark:border-white rounded hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all font-semibold">
              Start Free Trial
            </button>
          </div>

          {/* Professional Plan */}
          <div className="bg-black dark:bg-white border-2 border-black dark:border-white rounded-lg p-8 relative">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-zinc-700 dark:bg-zinc-300 text-white dark:text-black px-4 py-1 rounded-full text-sm font-semibold">
              Most Popular
            </div>
            <h3 className="text-2xl font-bold text-white dark:text-black mb-2">Professional</h3>
            <p className="text-zinc-300 dark:text-zinc-700 mb-6">For growing teams</p>
            <div className="mb-6">
              <span className="text-5xl font-bold text-white dark:text-black">$79</span>
              <span className="text-zinc-300 dark:text-zinc-700">/month</span>
            </div>
            <ul className="space-y-4 mb-8">
              <li className="flex items-start gap-3">
                <span className="text-white dark:text-black">✓</span>
                <span className="text-zinc-200 dark:text-zinc-800">Up to 20 team members</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-white dark:text-black">✓</span>
                <span className="text-zinc-200 dark:text-zinc-800">Unlimited languages</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-white dark:text-black">✓</span>
                <span className="text-zinc-200 dark:text-zinc-800">Advanced project management</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-white dark:text-black">✓</span>
                <span className="text-zinc-200 dark:text-zinc-800">AI translations (10,000/month)</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-white dark:text-black">✓</span>
                <span className="text-zinc-200 dark:text-zinc-800">Priority support</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-white dark:text-black">✓</span>
                <span className="text-zinc-200 dark:text-zinc-800">Custom integrations</span>
              </li>
            </ul>
            <button className="w-full px-6 py-3 bg-white dark:bg-black text-black dark:text-white rounded hover:bg-zinc-100 dark:hover:bg-zinc-900 transition-all font-semibold">
              Start Free Trial
            </button>
          </div>

          {/* Enterprise Plan */}
          <div className="bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8">
            <h3 className="text-2xl font-bold text-black dark:text-white mb-2">Enterprise</h3>
            <p className="text-zinc-600 dark:text-zinc-400 mb-6">For large organizations</p>
            <div className="mb-6">
              <span className="text-5xl font-bold text-black dark:text-white">Custom</span>
            </div>
            <ul className="space-y-4 mb-8">
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">Unlimited team members</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">Unlimited languages</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">Enterprise project management</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">Unlimited AI translations</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">Dedicated account manager</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">SLA & 24/7 support</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="text-black dark:text-white">✓</span>
                <span className="text-zinc-700 dark:text-zinc-300">On-premise deployment option</span>
              </li>
            </ul>
            <button className="w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-black dark:border-white rounded hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all font-semibold">
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        <h2 className="text-3xl font-bold text-black dark:text-white mb-8 text-center">
          Frequently Asked Questions
        </h2>
        <div className="space-y-6">
          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
              Can I change plans later?
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
            </p>
          </div>
          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
              What languages are supported?
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              We support over 50 languages including English, Spanish, French, German, Chinese, Japanese, Arabic, and many more.
            </p>
          </div>
          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
              Is there a free trial?
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              Yes! All plans come with a 14-day free trial. No credit card required.
            </p>
          </div>
          <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
              How accurate are the translations?
            </h3>
            <p className="text-zinc-600 dark:text-zinc-400">
              Our AI-powered translations are context-aware and continuously improving. For critical content, we also offer human review options.
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-zinc-200 dark:border-zinc-800 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-zinc-600 dark:text-zinc-400">
            <p>&copy; 2025 Stacks. Built for multilingual teams everywhere.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

