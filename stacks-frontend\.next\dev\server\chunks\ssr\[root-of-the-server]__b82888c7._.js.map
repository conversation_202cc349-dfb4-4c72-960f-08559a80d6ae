{"version": 3, "sources": [], "sections": [{"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/app/pricing/page.tsx"], "sourcesContent": ["export default function Pricing() {\n  return (\n    <div className=\"min-h-screen bg-zinc-50 dark:bg-zinc-900\">\n      {/* Navigation */}\n      <nav className=\"border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center gap-2\">\n              <a href=\"/\" className=\"flex items-center gap-2\">\n                <div className=\"w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center\">\n                  <span className=\"text-white dark:text-black font-bold text-lg\">S</span>\n                </div>\n                <span className=\"text-xl font-bold text-black dark:text-white\">Stacks</span>\n              </a>\n            </div>\n            <div className=\"flex items-center gap-6\">\n              <a href=\"/#features\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Features\n              </a>\n              <a href=\"/#how-it-works\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                How It Works\n              </a>\n              <a href=\"/pricing\" className=\"text-black dark:text-white font-medium transition-colors\">\n                Pricing\n              </a>\n              <a href=\"/about\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                About\n              </a>\n              <a href=\"/login\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Log In\n              </a>\n              <a href=\"/signup\" className=\"px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-medium\">\n                Sign Up\n              </a>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12\">\n        <div className=\"text-center\">\n          <h1 className=\"text-5xl sm:text-6xl font-bold text-black dark:text-white mb-6\">\n            Simple, Transparent Pricing\n          </h1>\n          <p className=\"text-xl text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto\">\n            Choose the plan that fits your team. All plans include AI-powered translations.\n          </p>\n        </div>\n      </section>\n\n      {/* Pricing Cards */}\n      <section className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20\">\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {/* Starter Plan */}\n          <div className=\"bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8\">\n            <h3 className=\"text-2xl font-bold text-black dark:text-white mb-2\">Starter</h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400 mb-6\">Perfect for small teams</p>\n            <div className=\"mb-6\">\n              <span className=\"text-5xl font-bold text-black dark:text-white\">$29</span>\n              <span className=\"text-zinc-600 dark:text-zinc-400\">/month</span>\n            </div>\n            <ul className=\"space-y-4 mb-8\">\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">Up to 5 team members</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">2 languages supported</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">Basic project management</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">AI translations (1,000/month)</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">Email support</span>\n              </li>\n            </ul>\n            <button className=\"w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-black dark:border-white rounded hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all font-semibold\">\n              Start Free Trial\n            </button>\n          </div>\n\n          {/* Professional Plan */}\n          <div className=\"bg-black dark:bg-white border-2 border-black dark:border-white rounded-lg p-8 relative\">\n            <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 bg-zinc-700 dark:bg-zinc-300 text-white dark:text-black px-4 py-1 rounded-full text-sm font-semibold\">\n              Most Popular\n            </div>\n            <h3 className=\"text-2xl font-bold text-white dark:text-black mb-2\">Professional</h3>\n            <p className=\"text-zinc-300 dark:text-zinc-700 mb-6\">For growing teams</p>\n            <div className=\"mb-6\">\n              <span className=\"text-5xl font-bold text-white dark:text-black\">$79</span>\n              <span className=\"text-zinc-300 dark:text-zinc-700\">/month</span>\n            </div>\n            <ul className=\"space-y-4 mb-8\">\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-white dark:text-black\">✓</span>\n                <span className=\"text-zinc-200 dark:text-zinc-800\">Up to 20 team members</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-white dark:text-black\">✓</span>\n                <span className=\"text-zinc-200 dark:text-zinc-800\">Unlimited languages</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-white dark:text-black\">✓</span>\n                <span className=\"text-zinc-200 dark:text-zinc-800\">Advanced project management</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-white dark:text-black\">✓</span>\n                <span className=\"text-zinc-200 dark:text-zinc-800\">AI translations (10,000/month)</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-white dark:text-black\">✓</span>\n                <span className=\"text-zinc-200 dark:text-zinc-800\">Priority support</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-white dark:text-black\">✓</span>\n                <span className=\"text-zinc-200 dark:text-zinc-800\">Custom integrations</span>\n              </li>\n            </ul>\n            <button className=\"w-full px-6 py-3 bg-white dark:bg-black text-black dark:text-white rounded hover:bg-zinc-100 dark:hover:bg-zinc-900 transition-all font-semibold\">\n              Start Free Trial\n            </button>\n          </div>\n\n          {/* Enterprise Plan */}\n          <div className=\"bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8\">\n            <h3 className=\"text-2xl font-bold text-black dark:text-white mb-2\">Enterprise</h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400 mb-6\">For large organizations</p>\n            <div className=\"mb-6\">\n              <span className=\"text-5xl font-bold text-black dark:text-white\">Custom</span>\n            </div>\n            <ul className=\"space-y-4 mb-8\">\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">Unlimited team members</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">Unlimited languages</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">Enterprise project management</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">Unlimited AI translations</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">Dedicated account manager</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">SLA & 24/7 support</span>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <span className=\"text-black dark:text-white\">✓</span>\n                <span className=\"text-zinc-700 dark:text-zinc-300\">On-premise deployment option</span>\n              </li>\n            </ul>\n            <button className=\"w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-black dark:border-white rounded hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all font-semibold\">\n              Contact Sales\n            </button>\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ Section */}\n      <section className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-20\">\n        <h2 className=\"text-3xl font-bold text-black dark:text-white mb-8 text-center\">\n          Frequently Asked Questions\n        </h2>\n        <div className=\"space-y-6\">\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-black dark:text-white mb-2\">\n              Can I change plans later?\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.\n            </p>\n          </div>\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-black dark:text-white mb-2\">\n              What languages are supported?\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              We support over 50 languages including English, Spanish, French, German, Chinese, Japanese, Arabic, and many more.\n            </p>\n          </div>\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-black dark:text-white mb-2\">\n              Is there a free trial?\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Yes! All plans come with a 14-day free trial. No credit card required.\n            </p>\n          </div>\n          <div className=\"bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-black dark:text-white mb-2\">\n              How accurate are the translations?\n            </h3>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Our AI-powered translations are context-aware and continuously improving. For critical content, we also offer human review options.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"border-t border-zinc-200 dark:border-zinc-800 py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center text-zinc-600 dark:text-zinc-400\">\n            <p>&copy; 2025 Stacks. Built for multilingual teams everywhere.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,MAAK;oCAAI,WAAU;;sDACpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+C;;;;;;;;;;;sDAEjE,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAa,WAAU;kDAA4F;;;;;;kDAG3H,8OAAC;wCAAE,MAAK;wCAAiB,WAAU;kDAA4F;;;;;;kDAG/H,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA2D;;;;;;kDAGxF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA4F;;;;;;kDAGvH,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA4F;;;;;;kDAGvH,8OAAC;wCAAE,MAAK;wCAAU,WAAU;kDAA0I;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9K,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;;;;;;0BAO9E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgD;;;;;;sDAChE,8OAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;8CAErD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;8CAGvD,8OAAC;oCAAO,WAAU;8CAA+N;;;;;;;;;;;;sCAMnP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA2J;;;;;;8CAG1K,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgD;;;;;;sDAChE,8OAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;8CAErD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;8CAGvD,8OAAC;oCAAO,WAAU;8CAAmJ;;;;;;;;;;;;sCAMvK,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;8CAElE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;8CAGvD,8OAAC;oCAAO,WAAU;8CAA+N;;;;;;;;;;;;;;;;;;;;;;;0BAQvP,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAiE;;;;;;kCAG/E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAIlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAIlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAIlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;0BAQtD,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf"}}]}