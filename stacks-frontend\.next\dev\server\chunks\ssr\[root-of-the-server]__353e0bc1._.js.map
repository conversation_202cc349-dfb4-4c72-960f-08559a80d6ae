{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/app/signup/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nexport default function Signup() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    password: \"\",\n    confirmPassword: \"\",\n    language: \"en\",\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle signup logic here\n    console.log(\"Signup data:\", formData);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-zinc-50 dark:bg-zinc-900 flex flex-col\">\n      {/* Navigation */}\n      <nav className=\"border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center gap-2\">\n              <a href=\"/\" className=\"flex items-center gap-2\">\n                <div className=\"w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center\">\n                  <span className=\"text-white dark:text-black font-bold text-lg\">S</span>\n                </div>\n                <span className=\"text-xl font-bold text-black dark:text-white\">Stacks</span>\n              </a>\n            </div>\n            <div className=\"flex items-center gap-6\">\n              <a href=\"/pricing\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Pricing\n              </a>\n              <a href=\"/about\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                About\n              </a>\n              <a href=\"/login\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Log In\n              </a>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Signup Form */}\n      <div className=\"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"w-full max-w-md\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl font-bold text-black dark:text-white mb-2\">\n              Create Your Account\n            </h1>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Start collaborating across languages today\n            </p>\n          </div>\n\n          <div className=\"bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Name Field */}\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-black dark:text-white mb-2\">\n                  Full Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors\"\n                  placeholder=\"John Doe\"\n                />\n              </div>\n\n              {/* Email Field */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-black dark:text-white mb-2\">\n                  Email Address\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              {/* Password Field */}\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-black dark:text-white mb-2\">\n                  Password\n                </label>\n                <input\n                  type=\"password\"\n                  id=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors\"\n                  placeholder=\"••••••••\"\n                />\n              </div>\n\n              {/* Confirm Password Field */}\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-black dark:text-white mb-2\">\n                  Confirm Password\n                </label>\n                <input\n                  type=\"password\"\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors\"\n                  placeholder=\"••••••••\"\n                />\n              </div>\n\n              {/* Language Preference */}\n              <div>\n                <label htmlFor=\"language\" className=\"block text-sm font-medium text-black dark:text-white mb-2\">\n                  Preferred Language\n                </label>\n                <select\n                  id=\"language\"\n                  name=\"language\"\n                  value={formData.language}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors\"\n                >\n                  <option value=\"en\">English</option>\n                  <option value=\"es\">Spanish</option>\n                  <option value=\"fr\">French</option>\n                  <option value=\"de\">German</option>\n                  <option value=\"zh\">Chinese</option>\n                  <option value=\"ja\">Japanese</option>\n                  <option value=\"ar\">Arabic</option>\n                  <option value=\"pt\">Portuguese</option>\n                  <option value=\"ru\">Russian</option>\n                  <option value=\"ko\">Korean</option>\n                </select>\n              </div>\n\n              {/* Submit Button */}\n              <button\n                type=\"submit\"\n                className=\"w-full px-6 py-3 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-semibold\"\n              >\n                Create Account\n              </button>\n\n              {/* Terms */}\n              <p className=\"text-sm text-zinc-600 dark:text-zinc-400 text-center\">\n                By signing up, you agree to our{\" \"}\n                <a href=\"#\" className=\"text-black dark:text-white hover:underline\">\n                  Terms of Service\n                </a>{\" \"}\n                and{\" \"}\n                <a href=\"#\" className=\"text-black dark:text-white hover:underline\">\n                  Privacy Policy\n                </a>\n              </p>\n            </form>\n\n            {/* Divider */}\n            <div className=\"relative my-6\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-zinc-300 dark:border-zinc-600\"></div>\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white dark:bg-zinc-800 text-zinc-600 dark:text-zinc-400\">\n                  Or continue with\n                </span>\n              </div>\n            </div>\n\n            {/* Social Login Buttons */}\n            <div className=\"space-y-3\">\n              <button className=\"w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-zinc-300 dark:border-zinc-600 rounded hover:border-black dark:hover:border-white transition-all font-medium flex items-center justify-center gap-2\">\n                Continue with Google\n              </button>\n              <button className=\"w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-zinc-300 dark:border-zinc-600 rounded hover:border-black dark:hover:border-white transition-all font-medium flex items-center justify-center gap-2\">\n                Continue with Microsoft\n              </button>\n            </div>\n\n            {/* Login Link */}\n            <p className=\"text-center text-zinc-600 dark:text-zinc-400 mt-6\">\n              Already have an account?{\" \"}\n              <a href=\"/login\" className=\"text-black dark:text-white font-semibold hover:underline\">\n                Log in\n              </a>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,UAAU;IACZ;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,gBAAgB;IAC9B;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,MAAK;oCAAI,WAAU;;sDACpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+C;;;;;;;;;;;sDAEjE,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA4F;;;;;;kDAGzH,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA4F;;;;;;kDAGvH,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA4F;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/H,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAKlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDAEtC,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAA4D;;;;;;8DAG5F,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA4D;;;;;;8DAG7F,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA4D;;;;;;8DAGhG,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAA4D;;;;;;8DAGvG,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,eAAe;oDAC/B,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA4D;;;;;;8DAGhG,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;;;;;;;;;;;;;sDAKvB,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAKD,8OAAC;4CAAE,WAAU;;gDAAuD;gDAClC;8DAChC,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DAA6C;;;;;;gDAE9D;gDAAI;gDACL;8DACJ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;8CAOvE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAkE;;;;;;;;;;;;;;;;;8CAOtF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAA2O;;;;;;sDAG7P,8OAAC;4CAAO,WAAU;sDAA2O;;;;;;;;;;;;8CAM/P,8OAAC;oCAAE,WAAU;;wCAAoD;wCACtC;sDACzB,8OAAC;4CAAE,MAAK;4CAAS,WAAU;sDAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpG"}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0]}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0]}}]}