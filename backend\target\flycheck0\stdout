{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.101","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\proc-macro2-1.0.101\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\proc-macro2-1.0.101\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\proc-macro2-87a5f0ce3b8cc4ee\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\proc-macro2-87a5f0ce3b8cc4ee\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.101","linked_libs":[],"linked_paths":[],"cfgs":["wrap_proc_macro","proc_macro_span_location","proc_macro_span_file"],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\proc-macro2-4b7a3dafd5f82b84\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicode-ident@1.0.19","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicode-ident-1.0.19\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicode_ident","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicode-ident-1.0.19\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libunicode_ident-189ca15178b593cc.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libunicode_ident-189ca15178b593cc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.41","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\quote-1.0.41\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\quote-1.0.41\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\quote-e6db9c3fe01e6580\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\quote-e6db9c3fe01e6580\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.52.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\windows_x86_64_msvc-34d12bac09911094\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\windows_x86_64_msvc-34d12bac09911094\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bytes@1.10.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytes-1.10.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"bytes","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytes-1.10.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libbytes-3ffb97e7420a825a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pin-project-lite@0.2.16","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-project-lite-0.2.16\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pin_project_lite","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-project-lite-0.2.16\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpin_project_lite-e248651c8d2f7cc0.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-core@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-core-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-core-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfutures_core-12e623482c41f8fb.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#itoa@1.0.15","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\itoa-1.0.15\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"itoa","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\itoa-1.0.15\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libitoa-2179d09a8cbaa087.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#fnv@1.0.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\fnv-1.0.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"fnv","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\fnv-1.0.7\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfnv-53f2353b3d631611.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#anyhow@1.0.100","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\anyhow-1.0.100\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\anyhow-1.0.100\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\anyhow-407480eec81b2c3f\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\anyhow-407480eec81b2c3f\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#either@1.15.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\either-1.15.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"either","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\either-1.15.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std","use_std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libeither-11bfc80e806bb44f.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libeither-11bfc80e806bb44f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#once_cell@1.21.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\once_cell-1.21.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"once_cell","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\once_cell-1.21.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","race","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libonce_cell-85196ba60341bfbe.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-sink@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-sink-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_sink","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-sink-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfutures_sink-1b406bc4ab2cea80.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#equivalent@1.0.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\equivalent-1.0.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"equivalent","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\equivalent-1.0.2\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libequivalent-a82a1228783d4ddb.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hashbrown@0.16.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hashbrown-0.16.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hashbrown","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hashbrown-0.16.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhashbrown-ac26a93fa943501d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pin-utils@0.1.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-utils-0.1.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pin_utils","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-utils-0.1.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpin_utils-8028ac6e40e7480e.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.41","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\quote-102a82c13fe8e5c0\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.101","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\proc-macro2-1.0.101\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"proc_macro2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\proc-macro2-1.0.101\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libproc_macro2-422ab01cb9b02ba1.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libproc_macro2-422ab01cb9b02ba1.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.52.6","linked_libs":[],"linked_paths":["native=C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\lib"],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\windows_x86_64_msvc-8f1c70f04d068791\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#anyhow@1.0.100","linked_libs":[],"linked_paths":[],"cfgs":["std_backtrace"],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\anyhow-a716e66f89dca77f\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http@1.3.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-1.3.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-1.3.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhttp-9a0b7a08a3be52f6.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#itertools@0.14.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\itertools-0.14.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"itertools","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\itertools-0.14.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","use_alloc","use_std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libitertools-a2c454b98c449043.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libitertools-a2c454b98c449043.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing-core@0.1.34","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-core-0.1.34\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tracing_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-core-0.1.34\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["once_cell","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtracing_core-6d1d7b7089f2d248.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#indexmap@2.11.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\indexmap-2.11.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"indexmap","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\indexmap-2.11.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libindexmap-1b99cf9dc3a9ffbc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#slab@0.4.11","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\slab-0.4.11\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"slab","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\slab-0.4.11\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libslab-2c88657cc0ba315c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pulldown-cmark@0.13.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pulldown-cmark-0.13.0\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pulldown-cmark-0.13.0\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\pulldown-cmark-92cbc76f13a1d1d0\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\pulldown-cmark-92cbc76f13a1d1d0\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#prettyplease@0.2.37","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prettyplease-0.2.37\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prettyplease-0.2.37\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\prettyplease-82a20187cb700a93\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\prettyplease-82a20187cb700a93\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#getrandom@0.3.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\getrandom-0.3.3\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\getrandom-0.3.3\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\getrandom-2229b43e2e21abee\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\getrandom-2229b43e2e21abee\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bytes@1.10.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytes-1.10.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"bytes","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bytes-1.10.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libbytes-f2c77389e8b34cbf.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libbytes-f2c77389e8b34cbf.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#memchr@2.7.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memchr-2.7.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"memchr","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memchr-2.7.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libmemchr-5963c4b4d7540ace.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libmemchr-5963c4b4d7540ace.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hashbrown@0.16.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hashbrown-0.16.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hashbrown","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hashbrown-0.16.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhashbrown-a99ecd4b921f7031.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhashbrown-a99ecd4b921f7031.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httparse-1.10.1\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httparse-1.10.1\\build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\httparse-7fcf39767b8eaed6\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\httparse-7fcf39767b8eaed6\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.41","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\quote-1.0.41\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"quote","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\quote-1.0.41\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libquote-c5727dce1eaf2b5d.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libquote-c5727dce1eaf2b5d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.52.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_x86_64_msvc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_x86_64_msvc-14a69c30625fb86e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#anyhow@1.0.100","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\anyhow-1.0.100\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"anyhow","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\anyhow-1.0.100\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libanyhow-f0daf8c125d61630.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libanyhow-f0daf8c125d61630.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http-body@1.0.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-body-1.0.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http_body","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-body-1.0.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhttp_body-c7dcb6bcdae21a9d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.52.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_x86_64_msvc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows_x86_64_msvc-0.52.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_x86_64_msvc-d5cbeee1bb7673ab.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_x86_64_msvc-d5cbeee1bb7673ab.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#prettyplease@0.2.37","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\prettyplease-451e74ce8fdac27f\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#getrandom@0.3.3","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\getrandom-03c333bf82899517\\out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#pulldown-cmark@0.13.0","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\pulldown-cmark-a3834d89ebbb18e0\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bitflags@2.9.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bitflags-2.9.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"bitflags","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\bitflags-2.9.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libbitflags-b366544d4fb77ade.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libbitflags-b366544d4fb77ade.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower-service@0.3.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-service-0.3.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower_service","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-service-0.3.3\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtower_service-7d33490080c14049.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-task@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-task-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_task","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-task-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfutures_task-dac378d6c02fc697.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cfg-if@1.0.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cfg-if-1.0.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cfg_if","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\cfg-if-1.0.3\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libcfg_if-0b3aadc8d41dde04.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libcfg_if-0b3aadc8d41dde04.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#equivalent@1.0.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\equivalent-1.0.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"equivalent","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\equivalent-1.0.2\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libequivalent-e22c3f4df5e6eed3.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libequivalent-e22c3f4df5e6eed3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#regex-syntax@0.8.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\regex-syntax-0.8.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"regex_syntax","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\regex-syntax-0.8.7\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std","unicode-bool"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libregex_syntax-7df1605ec1dc5234.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libregex_syntax-7df1605ec1dc5234.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicase@2.8.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicase-2.8.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicase","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\unicase-2.8.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libunicase-28d975c8b584526e.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libunicase-28d975c8b584526e.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","linked_libs":[],"linked_paths":[],"cfgs":["httparse_simd_neon_intrinsics","httparse_simd"],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\httparse-63879826a15e254c\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#syn@2.0.106","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\syn-2.0.106\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"syn","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\syn-2.0.106\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["clone-impls","default","derive","extra-traits","full","parsing","printing","proc-macro","visit-mut"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libsyn-0c1dccf3481e26d2.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libsyn-0c1dccf3481e26d2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-targets@0.52.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.52.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_targets","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.52.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_targets-087a1f209bdc7d58.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-targets@0.52.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.52.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_targets","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-targets-0.52.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_targets-dad7403f5d32ccb5.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_targets-dad7403f5d32ccb5.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#indexmap@2.11.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\indexmap-2.11.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"indexmap","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\indexmap-2.11.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libindexmap-b0204ff5bfcbcc06.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libindexmap-b0204ff5bfcbcc06.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pulldown-cmark@0.13.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pulldown-cmark-0.13.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pulldown_cmark","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pulldown-cmark-0.13.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpulldown_cmark-7eeefeb78e73715c.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpulldown_cmark-7eeefeb78e73715c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#getrandom@0.3.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\getrandom-0.3.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"getrandom","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\getrandom-0.3.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libgetrandom-edc50b742f810fe7.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libgetrandom-edc50b742f810fe7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#regex-automata@0.4.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\regex-automata-0.4.12\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"regex_automata","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\regex-automata-0.4.12\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","meta","nfa-pikevm","nfa-thompson","std","syntax","unicode-bool"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libregex_automata-c5cb0b086b97c243.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libregex_automata-c5cb0b086b97c243.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-util@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-util-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_util","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-util-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfutures_util-d9a39644e9917280.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.177","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libc-0.2.177\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libc-0.2.177\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\libc-7ec82ca5cf065d86\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\libc-7ec82ca5cf065d86\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#once_cell@1.21.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\once_cell-1.21.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"once_cell","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\once_cell-1.21.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","race","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libonce_cell-209d90754afe65a8.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libonce_cell-209d90754afe65a8.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#try-lock@0.2.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\try-lock-0.2.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"try_lock","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\try-lock-0.2.5\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtry_lock-b47856521b51915c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#fixedbitset@0.5.7","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\fixedbitset-0.5.7\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"fixedbitset","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\fixedbitset-0.5.7\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfixedbitset-561872d5f7e3cbfc.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfixedbitset-561872d5f7e3cbfc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#atomic-waker@1.1.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\atomic-waker-1.1.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"atomic_waker","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\atomic-waker-1.1.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libatomic_waker-86e90ab71ec90966.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#fastrand@2.3.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\fastrand-2.3.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"fastrand","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\fastrand-2.3.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfastrand-3b00ffb3ed5235e3.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfastrand-3b00ffb3ed5235e3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httparse-1.10.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"httparse","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httparse-1.10.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhttparse-7dfa445951a8e4a7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-channel@0.3.31","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-channel-0.3.31\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_channel","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\futures-channel-0.3.31\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libfutures_channel-77cbdadeff46009f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.59.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.59.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_sys","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.59.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["Wdk","Wdk_Foundation","Wdk_Storage","Wdk_Storage_FileSystem","Wdk_System","Wdk_System_IO","Win32","Win32_Foundation","Win32_Networking","Win32_Networking_WinSock","Win32_Security","Win32_Storage","Win32_Storage_FileSystem","Win32_System","Win32_System_IO","Win32_System_Pipes","Win32_System_SystemServices","Win32_System_Threading","Win32_System_WindowsProgramming","default"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_sys-7c012587610b2cbc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio-macros@2.5.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-macros-2.5.0\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"tokio_macros","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-macros-2.5.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\tokio_macros-dbc190c14cc75aeb.dll","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\tokio_macros-dbc190c14cc75aeb.dll.lib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\tokio_macros-dbc190c14cc75aeb.dll.exp","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\tokio_macros-dbc190c14cc75aeb.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing-attributes@0.1.30","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-attributes-0.1.30\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"tracing_attributes","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-attributes-0.1.30\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\tracing_attributes-dff60a96761825f9.dll","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\tracing_attributes-dff60a96761825f9.dll.lib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\tracing_attributes-dff60a96761825f9.dll.exp","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\tracing_attributes-dff60a96761825f9.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#prost-derive@0.14.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-derive-0.14.1\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"prost_derive","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-derive-0.14.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\prost_derive-8133d3360df08860.dll","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\prost_derive-8133d3360df08860.dll.lib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\prost_derive-8133d3360df08860.dll.exp","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\prost_derive-8133d3360df08860.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#prettyplease@0.2.37","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prettyplease-0.2.37\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"prettyplease","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prettyplease-0.2.37\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprettyplease-8bff925ba21de53c.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprettyplease-8bff925ba21de53c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.59.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.59.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"windows_sys","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\windows-sys-0.59.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["Win32","Win32_Foundation","Win32_Storage","Win32_Storage_FileSystem","default"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_sys-b399067a5ac3a103.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwindows_sys-b399067a5ac3a103.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pulldown-cmark-to-cmark@21.0.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pulldown-cmark-to-cmark-21.0.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pulldown_cmark_to_cmark","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pulldown-cmark-to-cmark-21.0.0\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpulldown_cmark_to_cmark-aaa24b70355c0fde.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpulldown_cmark_to_cmark-aaa24b70355c0fde.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#petgraph@0.7.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\petgraph-0.7.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"petgraph","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\petgraph-0.7.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpetgraph-997a89a19192e719.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpetgraph-997a89a19192e719.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#want@0.3.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\want-0.3.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"want","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\want-0.3.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libwant-4767ff879c80ad7c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#regex@1.12.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\regex-1.12.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"regex","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\regex-1.12.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std","unicode-bool"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libregex-8a85530ac9ca8c0c.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libregex-8a85530ac9ca8c0c.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.177","linked_libs":[],"linked_paths":[],"cfgs":["freebsd12"],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\libc-881a18ffa071e6c2\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#multimap@0.10.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\multimap-0.10.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"multimap","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\multimap-0.10.1\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libmultimap-8a1460fb2b07930c.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libmultimap-8a1460fb2b07930c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#smallvec@1.15.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\smallvec-1.15.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"smallvec","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\smallvec-1.15.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["const_generics","const_new"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libsmallvec-db4a9b4e84ae1788.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#heck@0.5.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\heck-0.5.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"heck","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\heck-0.5.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libheck-420969f9825c8e1b.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libheck-420969f9825c8e1b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","result","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\serde_core-f2dd468f88769cb3\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\serde_core-f2dd468f88769cb3\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#log@0.4.28","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.28\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"log","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.28\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\liblog-3636dd8cd326c56f.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\liblog-3636dd8cd326c56f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#socket2@0.6.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\socket2-0.6.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"socket2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\socket2-0.6.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["all"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libsocket2-69c3561bc81ec34e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mio@1.0.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mio-1.0.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"mio","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mio-1.0.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["net","os-ext","os-poll"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libmio-55aa873e5eddc90e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing@0.1.41","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tracing","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["attributes","default","std","tracing-attributes"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtracing-eb6bd7159baf4a43.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#prost@0.14.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-0.14.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"prost","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-0.14.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["derive","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprost-4e5bd24033ed2a8d.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprost-4e5bd24033ed2a8d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tempfile@3.23.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tempfile-3.23.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tempfile","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tempfile-3.23.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","getrandom"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtempfile-d5de93830846c933.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtempfile-d5de93830846c933.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#httpdate@1.0.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httpdate-1.0.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"httpdate","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\httpdate-1.0.3\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhttpdate-b264188b24c737f0.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#sync_wrapper@1.0.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sync_wrapper-1.0.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"sync_wrapper","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sync_wrapper-1.0.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libsync_wrapper-d837076d16f43d78.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower-layer@0.3.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-layer-0.3.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower_layer","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-layer-0.3.3\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtower_layer-31e16d504edbd156.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tonic-build@0.14.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tonic-build-0.14.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tonic_build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tonic-build-0.14.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["transport"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtonic_build-66d10749bee76d32.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtonic_build-66d10749bee76d32.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\serde_core-d76394380277e1dd\\out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.177","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libc-0.2.177\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"libc","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libc-0.2.177\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\liblibc-c2f2b13b66e36419.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http-body-util@0.1.3","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-body-util-0.1.3\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http_body_util","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\http-body-util-0.1.3\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhttp_body_util-22957268613ab7f2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime@0.3.17","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mime-0.3.17\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"mime","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\mime-0.3.17\\src\\lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libmime-fca8bbb6a814509a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pin-project-internal@1.1.10","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-project-internal-1.1.10\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"pin_project_internal","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-project-internal-1.1.10\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\pin_project_internal-c926e816bba05063.dll","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\pin_project_internal-c926e816bba05063.dll.lib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\pin_project_internal-c926e816bba05063.dll.exp","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\pin_project_internal-c926e816bba05063.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#memchr@2.7.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memchr-2.7.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"memchr","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\memchr-2.7.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libmemchr-1b36ac37216fb55d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#percent-encoding@2.3.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\percent-encoding-2.3.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"percent_encoding","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\percent-encoding-2.3.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpercent_encoding-3403d4c234de3081.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio@1.47.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.47.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.47.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["bytes","default","io-std","io-util","libc","macros","mio","net","rt","rt-multi-thread","socket2","sync","time","tokio-macros","windows-sys"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtokio-db7bb6696b7ce5b8.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#prost-types@0.14.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-types-0.14.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"prost_types","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-types-0.14.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":false,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprost_types-88dd230c2847b04d.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprost_types-88dd230c2847b04d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\serde_core-1.0.228\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","result","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libserde_core-cac72e0e29a12805.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#axum-core@0.5.5","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-core-0.5.5\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"axum_core","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-core-0.5.5\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libaxum_core-8e38d9653dc40006.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#matchit@0.8.4","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\matchit-0.8.4\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"matchit","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\matchit-0.8.4\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libmatchit-1f7aeddd9e06fb5a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pin-project@1.1.10","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-project-1.1.10\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pin_project","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pin-project-1.1.10\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libpin_project-4ee39fb3853d04ba.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#async-trait@0.1.89","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\async-trait-0.1.89\\Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"async_trait","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\async-trait-0.1.89\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\async_trait-61ad6f355f05a2dd.dll","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\async_trait-61ad6f355f05a2dd.dll.lib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\async_trait-61ad6f355f05a2dd.dll.exp","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\async_trait-61ad6f355f05a2dd.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#base64@0.22.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\base64-0.22.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"base64","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\base64-0.22.1\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libbase64-d22485985cdca7d1.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#prost@0.14.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-0.14.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"prost","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-0.14.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","derive","std"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprost-5a32797b23a60a7a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio-util@0.7.16","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-util-0.7.16\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio_util","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-util-0.7.16\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["codec","default","io"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtokio_util-dfa6a48251c115ed.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#prost-build@0.14.1","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-build-0.14.1\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"prost_build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\prost-build-0.14.1\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["cleanup-markdown","default","format"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprost_build-73d51aec34e42f8c.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libprost_build-73d51aec34e42f8c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio-stream@0.1.17","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-stream-0.1.17\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio_stream","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-stream-0.1.17\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["net"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtokio_stream-062d070e4235884d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#h2@0.4.12","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\h2-0.4.12\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"h2","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\h2-0.4.12\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libh2-813227f77a076a44.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower@0.5.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-0.5.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tower-0.5.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["__common","balance","buffer","discover","futures-core","futures-util","indexmap","limit","load","load-shed","make","pin-project-lite","ready-cache","slab","sync_wrapper","tokio","tokio-util","tracing","util"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtower-feaaabfcc80b0f5a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tonic-prost-build@0.14.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tonic-prost-build-0.14.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tonic_prost_build","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tonic-prost-build-0.14.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["cleanup-markdown","default","transport"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtonic_prost_build-940f7a116169a1c3.rlib","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtonic_prost_build-940f7a116169a1c3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hyper@1.7.0","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-1.7.0\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hyper","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-1.7.0\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["client","default","http1","http2","server"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhyper-27b165d9731a6a24.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"path+file:///C:/Users/<USER>/Projects/projects/stacks/backend#0.1.0","manifest_path":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\build.rs","edition":"2024","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\backend-8c21dfa211ef7785\\build-script-build.exe","C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\build\\backend-8c21dfa211ef7785\\build_script_build.pdb"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#axum@0.8.6","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.8.6\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"axum","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.8.6\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libaxum-4c411dc17c3b92ec.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hyper-util@0.1.17","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-util-0.1.17\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hyper_util","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-util-0.1.17\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["client","client-legacy","default","http1","http2","server","server-auto","service","tokio"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhyper_util-d01a91e87c593a9e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hyper-timeout@0.5.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-timeout-0.5.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hyper_timeout","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\hyper-timeout-0.5.2\\src\\lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libhyper_timeout-5151272a189f21aa.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tonic@0.14.2","manifest_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tonic-0.14.2\\Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tonic","src_path":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tonic-0.14.2\\src\\lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["channel","codegen","default","router","server","transport"],"filenames":["C:\\Users\\<USER>\\Projects\\projects\\stacks\\backend\\target\\debug\\deps\\libtonic-65648708d69a68e7.rmeta"],"executable":null,"fresh":true}
{"reason":"build-finished","success":false}
