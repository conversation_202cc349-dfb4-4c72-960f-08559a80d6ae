{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/app/login/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nexport default function Login() {\n  const [formData, setFormData] = useState({\n    email: \"\",\n    password: \"\",\n    rememberMe: false,\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle login logic here\n    console.log(\"Login data:\", formData);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.type === \"checkbox\" ? e.target.checked : e.target.value;\n    setFormData({\n      ...formData,\n      [e.target.name]: value,\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-zinc-50 dark:bg-zinc-900 flex flex-col\">\n      {/* Navigation */}\n      <nav className=\"border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center gap-2\">\n              <a href=\"/\" className=\"flex items-center gap-2\">\n                <div className=\"w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center\">\n                  <span className=\"text-white dark:text-black font-bold text-lg\">S</span>\n                </div>\n                <span className=\"text-xl font-bold text-black dark:text-white\">Stacks</span>\n              </a>\n            </div>\n            <div className=\"flex items-center gap-6\">\n              <a href=\"/pricing\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                Pricing\n              </a>\n              <a href=\"/about\" className=\"text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors\">\n                About\n              </a>\n              <a href=\"/signup\" className=\"px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-medium\">\n                Sign Up\n              </a>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Login Form */}\n      <div className=\"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"w-full max-w-md\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl font-bold text-black dark:text-white mb-2\">\n              Welcome Back\n            </h1>\n            <p className=\"text-zinc-600 dark:text-zinc-400\">\n              Log in to your Stacks account\n            </p>\n          </div>\n\n          <div className=\"bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Email Field */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-black dark:text-white mb-2\">\n                  Email Address\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              {/* Password Field */}\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-black dark:text-white mb-2\">\n                  Password\n                </label>\n                <input\n                  type=\"password\"\n                  id=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors\"\n                  placeholder=\"••••••••\"\n                />\n              </div>\n\n              {/* Remember Me & Forgot Password */}\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"rememberMe\"\n                    name=\"rememberMe\"\n                    checked={formData.rememberMe}\n                    onChange={handleChange}\n                    className=\"w-4 h-4 rounded border-zinc-300 dark:border-zinc-600 text-black focus:ring-black dark:focus:ring-white\"\n                  />\n                  <label htmlFor=\"rememberMe\" className=\"ml-2 text-sm text-zinc-700 dark:text-zinc-300\">\n                    Remember me\n                  </label>\n                </div>\n                <a href=\"#\" className=\"text-sm text-black dark:text-white hover:underline\">\n                  Forgot password?\n                </a>\n              </div>\n\n              {/* Submit Button */}\n              <button\n                type=\"submit\"\n                className=\"w-full px-6 py-3 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-semibold\"\n              >\n                Log In\n              </button>\n            </form>\n\n            {/* Divider */}\n            <div className=\"relative my-6\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-zinc-300 dark:border-zinc-600\"></div>\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white dark:bg-zinc-800 text-zinc-600 dark:text-zinc-400\">\n                  Or continue with\n                </span>\n              </div>\n            </div>\n\n            {/* Social Login Buttons */}\n            <div className=\"space-y-3\">\n              <button className=\"w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-zinc-300 dark:border-zinc-600 rounded hover:border-black dark:hover:border-white transition-all font-medium flex items-center justify-center gap-2\">\n                <span>🔍</span>\n                Continue with Google\n              </button>\n              <button className=\"w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-zinc-300 dark:border-zinc-600 rounded hover:border-black dark:hover:border-white transition-all font-medium flex items-center justify-center gap-2\">\n                <span>💼</span>\n                Continue with Microsoft\n              </button>\n            </div>\n\n            {/* Signup Link */}\n            <p className=\"text-center text-zinc-600 dark:text-zinc-400 mt-6\">\n              Don't have an account?{\" \"}\n              <a href=\"/signup\" className=\"text-black dark:text-white font-semibold hover:underline\">\n                Sign up\n              </a>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;QACvC,OAAO;QACP,UAAU;QACV,YAAY;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,0BAA0B;QAC1B,QAAQ,GAAG,CAAC,eAAe;IAC7B;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,EAAE,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,MAAM,CAAC,OAAO,GAAG,EAAE,MAAM,CAAC,KAAK;QAC9E,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;QACnB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,MAAK;oCAAI,WAAU;;sDACpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+C;;;;;;;;;;;sDAEjE,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA4F;;;;;;kDAGzH,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA4F;;;;;;kDAGvH,8OAAC;wCAAE,MAAK;wCAAU,WAAU;kDAA0I;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9K,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAKlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDAEtC,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA4D;;;;;;8DAG7F,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA4D;;;;;;8DAGhG,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,SAAS,SAAS,UAAU;4DAC5B,UAAU;4DACV,WAAU;;;;;;sEAEZ,8OAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAAgD;;;;;;;;;;;;8DAIxF,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAqD;;;;;;;;;;;;sDAM7E,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAkE;;;;;;;;;;;;;;;;;8CAOtF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;8DAAK;;;;;;gDAAS;;;;;;;sDAGjB,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;8DAAK;;;;;;gDAAS;;;;;;;;;;;;;8CAMnB,8OAAC;oCAAE,WAAU;;wCAAoD;wCACxC;sDACvB,8OAAC;4CAAE,MAAK;4CAAU,WAAU;sDAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrG"}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0]}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/projects/stacks/stacks-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0]}}]}