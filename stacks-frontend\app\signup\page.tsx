"use client";

import { useState } from "react";

export default function Signup() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    language: "en",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle signup logic here
    console.log("Signup data:", formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <div className="min-h-screen bg-zinc-50 dark:bg-zinc-900 flex flex-col">
      {/* Navigation */}
      <nav className="border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-2">
              <a href="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-black dark:bg-white rounded flex items-center justify-center">
                  <span className="text-white dark:text-black font-bold text-lg">S</span>
                </div>
                <span className="text-xl font-bold text-black dark:text-white">Stacks</span>
              </a>
            </div>
            <div className="flex items-center gap-6">
              <a href="/pricing" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Pricing
              </a>
              <a href="/about" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                About
              </a>
              <a href="/login" className="text-zinc-700 hover:text-black dark:text-zinc-300 dark:hover:text-white transition-colors">
                Log In
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Signup Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-black dark:text-white mb-2">
              Create Your Account
            </h1>
            <p className="text-zinc-600 dark:text-zinc-400">
              Start collaborating across languages today
            </p>
          </div>

          <div className="bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name Field */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-black dark:text-white mb-2">
                  Full Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors"
                  placeholder="John Doe"
                />
              </div>

              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-black dark:text-white mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-black dark:text-white mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors"
                  placeholder="••••••••"
                />
              </div>

              {/* Confirm Password Field */}
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-black dark:text-white mb-2">
                  Confirm Password
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors"
                  placeholder="••••••••"
                />
              </div>

              {/* Language Preference */}
              <div>
                <label htmlFor="language" className="block text-sm font-medium text-black dark:text-white mb-2">
                  Preferred Language
                </label>
                <select
                  id="language"
                  name="language"
                  value={formData.language}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-zinc-50 dark:bg-zinc-900 border border-zinc-300 dark:border-zinc-600 rounded text-black dark:text-white focus:outline-none focus:border-black dark:focus:border-white transition-colors"
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="zh">Chinese</option>
                  <option value="ja">Japanese</option>
                  <option value="ar">Arabic</option>
                  <option value="pt">Portuguese</option>
                  <option value="ru">Russian</option>
                  <option value="ko">Korean</option>
                </select>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                className="w-full px-6 py-3 bg-black dark:bg-white text-white dark:text-black rounded hover:bg-zinc-800 dark:hover:bg-zinc-100 transition-all font-semibold"
              >
                Create Account
              </button>

              {/* Terms */}
              <p className="text-sm text-zinc-600 dark:text-zinc-400 text-center">
                By signing up, you agree to our{" "}
                <a href="#" className="text-black dark:text-white hover:underline">
                  Terms of Service
                </a>{" "}
                and{" "}
                <a href="#" className="text-black dark:text-white hover:underline">
                  Privacy Policy
                </a>
              </p>
            </form>

            {/* Divider */}
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-zinc-300 dark:border-zinc-600"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-zinc-800 text-zinc-600 dark:text-zinc-400">
                  Or continue with
                </span>
              </div>
            </div>

            {/* Social Login Buttons */}
            <div className="space-y-3">
              <button className="w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-zinc-300 dark:border-zinc-600 rounded hover:border-black dark:hover:border-white transition-all font-medium flex items-center justify-center gap-2">
                Continue with Google
              </button>
              <button className="w-full px-6 py-3 bg-white dark:bg-zinc-900 text-black dark:text-white border-2 border-zinc-300 dark:border-zinc-600 rounded hover:border-black dark:hover:border-white transition-all font-medium flex items-center justify-center gap-2">
                Continue with Microsoft
              </button>
            </div>

            {/* Login Link */}
            <p className="text-center text-zinc-600 dark:text-zinc-400 mt-6">
              Already have an account?{" "}
              <a href="/login" className="text-black dark:text-white font-semibold hover:underline">
                Log in
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

